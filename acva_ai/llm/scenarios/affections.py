from typing import Dict, List, Optional

from acva_ai.llm.azure_call import call_llm_async
from acva_ai.llm.helpers import parse_json_output
from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.utils.usage import ResponseUsage

EXTRACT_AFFECTIONS_PROMPT = """
This is a transcript in {language} from a medical visit, obtained through a speech-to-text software. 
Your task is to extract all the references to patient affections, symptoms, diseases, syndromes.

Because this is a transcript from a speech-to-text software we want to make sure that all the names of affections, diseases, symptoms are correctly identified, even if they are not spelled correctly or if there are minor errors in the transcript.

You must extract all the named affections, diseases, symptoms or related text (even if they are discussed without any particular name)

This is the transcript:
{transcript}

Extract all the references to patient affections, symptoms, diseases, syndromes. 

Respond in {language} 

Make a list of all the affections mentioned in the transcript in a JSON format, with the following structure:
JSON:
- affection_name: <name of the affection in {language}> if spelled, if not, None
- affection_context: <context / explanations in which the affection is mentioned, the symptoms or any other relevant information about that particular affection in the discussion in {language}>

Return the JSON list without any other commentary.
"""


async def extract_affections(
    transcript: str,
    language: str = "Romanian",
    response_usage: Optional[ResponseUsage] = None,
    # TODO Codrin: Why are quotes needed?
    llm_orchestrator: Optional["LLMOrchestrator"] = None,
) -> List:
    """
    Extract affections from a transcript using an LLM.

    Args:
        transcript: The transcript to process
        language: Language of the transcript
        response_usage: Optional ResponseUsage object to track costs
        llm_orchestrator: Optional LLM orchestrator for provider selection

    Returns:
        List of extracted affections
    """
    check_prompt = EXTRACT_AFFECTIONS_PROMPT.format(
        transcript=transcript, language=language
    )

    # Use orchestrator if provided, otherwise fall back to direct Azure call
    if llm_orchestrator:
        response = await llm_orchestrator.call_llm(
            prompt=check_prompt, response_usage=response_usage
        )
    else:
        response = await call_llm_async(check_prompt, response_usage=response_usage)

    response_dict = await parse_json_output(response)

    results = []
    for item in response_dict:
        name = item.get("affection_name", None)
        context = item.get("affection_context", None)
        if context:
            results.append([name, context])

    return results
