import asyncio
import json
import logging
import traceback
from typing import Dict, List, Optional, Tuple

from acva_ai.database import mongo_instance
from acva_ai.llm.llm_providers.azure_call import call_llm_async
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport
from acva_ai.utils.usage import ResponseUsage
from acva_ai.llm.llm_orchestrator import LLMOrchestrator

logger = logging.getLogger(__name__)

VERIFICATION_PROMPT = """
You are analyzing a medical transcript in {language} to determine if a specific topic is mentioned.

TRANSCRIPT:
```
{transcript}
```

QUESTION: Is the topic '{section_key}' mentioned or discussed in any way in this transcript?

Respond with only a single word: 'true' if the topic is mentioned, or 'false' if it is not.
"""


async def extract_medical_report_section(
    transcript: str,
    section_key: str,
    section_prompt: str,
    language: str = "Romanian",
    response_usage: Optional[ResponseUsage] = None,
    llm_orchestrator: Optional[LLMOrchestrator] = None,
) -> <PERSON><PERSON>[str, Optional[str]]:
    """
    Extract a specific section of the medical report from the transcript.

    Args:
        transcript: The medical conversation transcript
        section_key: The section key (e.g., "DIAGNOSTIC", "PLAN_DE_TRATAMENT")
        section_prompt: The prompt template for this section
        language: The language of the transcript (default: Romanian)
        response_usage: Optional ResponseUsage object to track API usage
        llm_orchestrator: Optional LLM orchestrator for provider selection

    Returns:
        Tuple of (section_key, extracted_information as plain text)
    """
    if response_usage is None:
        response_usage = ResponseUsage()

    # Handle empty transcript case
    if not transcript or transcript.strip() == "":
        logger.warning(f"Empty transcript provided for section {section_key}")
        return section_key, None

    # First check if this section is mentioned in the transcript
    verification_prompt = VERIFICATION_PROMPT.format(
        transcript=transcript, section_key=section_key, language=language
    )

    try:
        # Use orchestrator if provided, otherwise fall back to direct Azure call
        if llm_orchestrator:
            verification_response = await llm_orchestrator.call_llm(
                prompt=verification_prompt,
                response_usage=response_usage,
            )
        else:
            verification_response = await call_llm_async(
                prompt=verification_prompt,
                response_usage=response_usage,
            )

        # Parse the verification response - simplified for boolean extraction
        verification_response = verification_response.strip().lower()
        is_present = "true" in verification_response

        logger.debug(
            f"Verification for {section_key}: {is_present} (raw response: {verification_response})"
        )

        # If the section is present, extract the information
        if is_present:
            formatted_prompt = section_prompt.format(
                transcript=transcript,
            )

            # Use orchestrator if provided, otherwise fall back to direct Azure call
            if llm_orchestrator:
                extraction_response = await llm_orchestrator.call_llm(
                    prompt=formatted_prompt,
                    response_usage=response_usage,
                )
            else:
                extraction_response = await call_llm_async(
                    prompt=formatted_prompt,
                    response_usage=response_usage,
                )

            # Return the raw text response without parsing
            return section_key, extraction_response.strip()
        else:
            return section_key, None

    except Exception as e:
        logger.error(f"Error processing section {section_key}: {str(e)}")
        return section_key, None


async def generate_report_summary(
    report: dict,
    language: str = "Romanian",
    response_usage: Optional[ResponseUsage] = None,
) -> str:
    """
    Generate a human-readable summary of the medical report.

    Args:
        report: Dictionary containing the structured medical report sections
        language: The language to use for the summary (default: Romanian)
        response_usage: Optional ResponseUsage object to track API usage

    Returns:
        A formatted string summary of the medical report
    """
    if response_usage is None:
        response_usage = ResponseUsage()

    # Skip if report is empty
    if not report or all(v is None for v in report.values()):
        return "No medical report information available."

    # Create a prompt for summarizing the report
    summary_prompt = f"""
    Create a concise, well-formatted summary of the following medical report information in {language}.
    Use professional medical language and organize the information clearly.
    
    Report data:
    {json.dumps(report, indent=2, ensure_ascii=False)}
    
    Format the summary with appropriate headings for each section that has information.
    Respond in {language}.
    """

    # Call LLM to generate summary
    summary_response = await call_llm_async(
        prompt=summary_prompt,
        response_usage=response_usage,
    )

    return summary_response


async def _build_medical_report(
    transcript: str,
    language: str = "Romanian",
    response_usage: Optional[ResponseUsage] = None,
    llm_orchestrator: Optional[LLMOrchestrator] = None,
) -> Dict[str, str]:
    """
    Internal function to build a structured medical report from the transcript.

    Args:
        transcript: The medical conversation transcript
        language: The language of the transcript (default: Romanian)
        response_usage: Optional ResponseUsage object to track API usage
        llm_orchestrator: Optional LLM orchestrator for provider selection

    Returns:
        Dictionary containing structured medical report sections
    """
    # Import here to avoid circular imports
    from acva_ai.utils.prompts import prompt_templates

    if response_usage is None:
        response_usage = ResponseUsage()

    # Process all sections concurrently
    tasks = [
        extract_medical_report_section(
            transcript=transcript,
            section_key=key,
            section_prompt=prompt_template,
            language=language,
            response_usage=response_usage,
            llm_orchestrator=llm_orchestrator,
        )
        for key, prompt_template in prompt_templates.items()
    ]
    results = await asyncio.gather(*tasks)

    # Combine results into a single dictionary
    responses = {key: value for key, value in results}

    return responses


async def build_medical_report_with_pipeline(
    task_id: str,
    transcript: str,
    processing_status: Optional[ProcessingStatus],
    visit_report: Optional[VisitReport],
    language: str = "Romanian",
    response_usage: Optional[ResponseUsage] = None,
    llm_orchestrator: Optional[LLMOrchestrator] = None,
):
    """
    Processes a transcript and generates a structured medical report.

    Args:
        task_id: Unique identifier for the task
        transcript: The transcript to process
        processing_status: Processing status object to update
        visit_report: Visit report object to update
        language: Language of the transcript
        response_usage: Optional ResponseUsage object to track costs
        llm_orchestrator: Optional LLMOrchestrator to use for LLM calls
    """
    logger.info(f"[Task {task_id}] Starting medical report generation")
    processing_status.start_stage("medical_report")
    mongo_instance.update_processing_status(task_id, processing_status.dict())

    # Use the provided orchestrator or create a default one
    if llm_orchestrator is None:
        from acva_ai.pipeline.main import get_llm_orchestrator

        llm_orchestrator = get_llm_orchestrator()

    try:
        medical_report = await _build_medical_report(
            transcript=transcript,
            language=language,
            response_usage=response_usage,
        )
        visit_report.medical_report = medical_report
        processing_status.complete_stage("medical_report")
        logger.info(
            f"[Task {task_id}] Completed medical report generation successfully"
        )

    except Exception as e:
        stack_trace = traceback.format_exc()
        processing_status.fail_stage("medical_report", e, stack_trace)
        processing_status.finalize()
        logger.error(
            f"[Task {task_id}] Error in medical report generation {e}\n{stack_trace}"
        )

    finally:
        mongo_instance.update_processing_status(task_id, processing_status.dict())
