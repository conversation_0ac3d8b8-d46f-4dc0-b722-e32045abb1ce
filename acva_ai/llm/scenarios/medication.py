from typing import Dict, List, Optional

from acva_ai.llm.llm_orchestrator import LLMOrchestrator
from acva_ai.utils.usage import ResponseUsage
from acva_ai.llm.helpers import parse_json_output

import logging

logger = logging.getLogger(__name__)

EXTRACT_MEDICATIONS_PROMPT = """
This is a transcript in {language} from a medical visit, obtained through a speech-to-text software. 
Your task is to extract all the references to medications, drugs, prescription medicine, including names of the medications.

Because this is a transcript from a speech-to-text software we want to make sure that all the names of medications are correctly identified, even if they are not spelled correctly or if there are minor errors in the transcript.

You must extract all the named medications or related text (even if a common medication is discussed without any particular name)

This is the transcript:
{transcript}

Extract all the references to medications, drugs, prescription medicine. Include only one mention per medication entity.

Respond in {language} 

Make a list of all the medications mentioned in the transcript in a JSON format, with the following structure:
JSON:
- medication_name: <name of the medication in {language}> if spelled, if not, None
- medication_context: <context / explanations in which the medication is mentioned, the symptoms for which is prescribed or any other relevant information about that particular medication in the discussion in {language}>

Return the JSON list without any other commentary.
"""


async def extract_medication(
    transcript: str,
    language: str = "Romanian",
    response_usage: Optional[ResponseUsage] = None,
    llm_orchestrator: Optional[LLMOrchestrator] = None,
) -> List:
    """
    Extract medications from a transcript using an LLM.

    Args:
        transcript: The transcript to process
        language: Language of the transcript
        response_usage: Optional ResponseUsage object to track costs
        llm_orchestrator: Optional LLMOrchestrator to use for LLM calls

    Returns:
        List of extracted medications
    """
    check_prompt = EXTRACT_MEDICATIONS_PROMPT.format(
        transcript=transcript, language=language
    )

    # Use the provided orchestrator or create a default one
    if llm_orchestrator is None:
        from acva_ai.pipeline.main import get_llm_orchestrator

        llm_orchestrator = get_llm_orchestrator()

    response = await llm_orchestrator.call_llm(
        prompt=check_prompt, response_usage=response_usage
    )

    response_dict = await parse_json_output(response)

    results = []
    for item in response_dict:
        name = item.get("medication_name", None)
        context = item.get("medication_context", None)
        if context:
            results.append([name, context])

    return results
