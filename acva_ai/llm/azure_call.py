import asyncio
import hashlib
import json
import logging
import os
import random
import re
import time
import uuid
from typing import Any, Dict, List, Optional, Union

import aiohttp
import numpy as np

from acva_ai._params import (
    AZURE_API_VERSION,
    AZURE_OPENAI_API_KEY,
    AZURE_OPENAI_ENDPOINT,
    CACHE_DIR,
    OPENAI_MODEL_ID,
    OPENAI_PRICING,
)
from acva_ai.llm.llm_cache import _generate_llm_cache_filename
from acva_ai.llm.rate_limiter import AzureOpenAIRateLimiter, RateLimitConfig
from acva_ai.utils.usage import LLMUsage, ResponseUsage

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

LLM_CALLS_CACHE_DIR = os.path.join(CACHE_DIR, "openai")
EMBEDDING_CACHE_DIR = os.path.join(CACHE_DIR, "embeddings")
os.makedirs(LLM_CALLS_CACHE_DIR, exist_ok=True)
os.makedirs(EMBEDDING_CACHE_DIR, exist_ok=True)

# Initialize the rate limiter with Azure OpenAI limits
rate_limiter = AzureOpenAIRateLimiter(
    {
        "gpt-4o": RateLimitConfig(
            tokens_per_minute=250000,  # 250k tokens per minute
            requests_per_minute=1500,  # 1500 requests per minute
        ),
        # Add other models as needed with their specific limits
    }
)


def estimate_tokens(text: str) -> int:
    """
    Estimate the number of tokens in a text string.
    This is a rough estimate: ~4 characters per token for English text.

    Args:
        text: The text to estimate tokens for

    Returns:
        Estimated token count
    """
    return len(text) // 4 + 1


def _generate_embedding_cache_filename(text: str, model_id: str) -> str:
    """
    Generate a stable filename for the given embedding parameters.

    Args:
        text: The text to embed
        model_id: The embedding model ID

    Returns:
        Path to the cache file
    """
    # Create a unique hash based on the text and model
    text_hash = hashlib.md5(text.encode("utf-8")).hexdigest()
    cache_filename = f"{model_id}_{text_hash}.npy"
    return os.path.join(EMBEDDING_CACHE_DIR, cache_filename)


# async def call_llm_async(
#     prompt: str,
#     model_id: str = OPENAI_MODEL_ID,  # This is your Azure deployment name
#     max_tokens: int = 1000,
#     use_cache: bool = False,
#     response_usage: Optional[ResponseUsage] = None,
#     max_retries: int = 5,
#     initial_retry_delay: float = 1.0,
#     temperature: float = 0,
# ) -> str:
#     """
#     Asynchronous call to Azure OpenAI Chat Completion with caching and rate limit handling.
#
#     Args:
#         prompt: The prompt to send to the model
#         model_id: The model ID (Azure deployment name)
#         max_tokens: Maximum tokens to generate
#         use_cache: Whether to use cached responses
#         response_usage: Optional ResponseUsage object to track costs
#         max_retries: Maximum number of retries for rate limit errors
#         initial_retry_delay: Initial delay before retrying (will increase exponentially)
#         temperature: Temperature parameter for the model
#         response_format: Optional response format specification
#
#     Returns:
#         The model's response as a string
#     """
#     task_id = None
#     # Try to extract task ID from the current context if it's in the prompt
#     task_id_match = re.search(r"\[Task ([a-f0-9-]+)\]", prompt)
#     if task_id_match:
#         task_id = task_id_match.group(1)
#
#     log_prefix = f"[Task {task_id}] " if task_id else ""
#
#     # Check cache first
#     cache_filepath = _generate_llm_cache_filename(prompt, model_id, max_tokens)
#     if os.path.isfile(cache_filepath) and use_cache:
#         try:
#             with open(cache_filepath, "r", encoding="utf-8") as f:
#                 cached_response = f.read()
#             logger.info(f"{log_prefix}Loaded async LLM response from cache.")
#             return cached_response
#         except Exception as e:
#             logger.warning(f"{log_prefix}Error reading cache file: {e}")
#
#     # Estimate tokens for this request (prompt + max response)
#     estimated_input_tokens = estimate_tokens(prompt)
#     estimated_total_tokens = estimated_input_tokens + max_tokens
#
#     # Wait if needed to comply with rate limits
#     await rate_limiter.wait_if_needed(model_id, estimated_total_tokens, task_id)
#
#     # Build the Azure-specific endpoint URL
#     url = f"{AZURE_OPENAI_ENDPOINT}/openai/deployments/{model_id}/chat/completions?api-version={AZURE_API_VERSION}"
#
#     headers = {
#         "api-key": OPENAI_API_KEY,  # Use your Azure API key here
#         "Content-Type": "application/json",
#     }
#
#     payload = {
#         "max_tokens": max_tokens,
#         "messages": [{"role": "system", "content": prompt}],
#         "temperature": temperature,
#     }
#
#     retry_count = 0
#     retry_delay = initial_retry_delay
#
#     while True:
#         try:
#             async with aiohttp.ClientSession() as session:
#                 async with session.post(
#                     url, headers=headers, json=payload
#                 ) as response_obj:
#                     response_data = await response_obj.json()
#
#                     # Check for rate limit error
#                     if response_data.get("error") is not None:
#                         error = response_data["error"]
#                         error_code = error.get("code")
#
#                         if error_code == "429" or error_code == "rate_limit_exceeded":
#                             # Extract wait time if available in the error message
#                             wait_time = retry_delay
#                             wait_time_match = re.search(
#                                 r"Please try again in (\d+)ms", error.get("message", "")
#                             )
#                             if wait_time_match:
#                                 wait_time = max(
#                                     float(wait_time_match.group(1)) / 1000.0,
#                                     retry_delay,
#                                 )
#
#                             if retry_count < max_retries:
#                                 retry_count += 1
#                                 logger.warning(
#                                     f"{log_prefix}Rate limit reached for {model_id}, waiting {wait_time:.2f}s "
#                                     f"(retry {retry_count}/{max_retries})"
#                                 )
#                                 await asyncio.sleep(wait_time)
#                                 # Increase retry delay for next attempt (exponential backoff)
#                                 retry_delay = min(
#                                     retry_delay * 2, 60.0
#                                 )  # Cap at 60 seconds
#                                 continue
#                             else:
#                                 logger.error(
#                                     f"{log_prefix}Rate limit exceeded after {max_retries} retries. "
#                                     f"Error: {error.get('message')}"
#                                 )
#                                 raise Exception(
#                                     f"Rate limit exceeded after {max_retries} retries"
#                                 )
#                         else:
#                             # Other API error
#                             logger.error(f"{log_prefix}API error: {error}")
#                             raise Exception(f"API error: {error}")
#
#                     # Check for content filter
#                     finish_reason = response_data.get("choices", [{}])[0].get(
#                         "finish_reason"
#                     )
#                     if finish_reason == "content_filter":
#                         logger.warning(
#                             f"{log_prefix}Content filter triggered. Modifying prompt to avoid filtering."
#                         )
#
#                         # Add a stronger clinical context disclaimer to bypass content filter
#                         modified_prompt = (
#                             "IMPORTANT: This is a MEDICAL CONTEXT. The following content is strictly for clinical "
#                             "purposes and should not be filtered. Medical discussions may include sensitive topics "
#                             "that are necessary for proper diagnosis and treatment.\n\n"
#                             + prompt
#                         )
#
#                         # Try again with modified prompt
#                         if retry_count < max_retries:
#                             retry_count += 1
#                             payload["messages"] = [
#                                 {"role": "system", "content": modified_prompt}
#                             ]
#                             logger.info(
#                                 f"{log_prefix}Retrying with modified prompt to avoid content filter."
#                             )
#                             await asyncio.sleep(1)  # Brief pause before retry
#                             continue
#                         else:
#                             logger.error(
#                                 f"{log_prefix}Content filter persists after {max_retries} retries."
#                             )
#                             return "Content filtered by Azure OpenAI. Please modify your prompt to avoid sensitive content."
#
#                     # Process successful response
#                     try:
#                         # Check if the message contains content
#                         if "content" in response_data["choices"][0].get("message", {}):
#                             response_msg = response_data["choices"][0]["message"][
#                                 "content"
#                             ]
#                         else:
#                             # Handle case where content is missing (could be due to partial content filtering)
#                             logger.warning(
#                                 f"{log_prefix}Response missing content field. Response: {response_data}"
#                             )
#
#                             # Check if there's a refusal message
#                             refusal = (
#                                 response_data["choices"][0]
#                                 .get("message", {})
#                                 .get("refusal")
#                             )
#                             if refusal:
#                                 return f"The AI refused to respond: {refusal}"
#
#                             # If we have a finish_reason but no content, create a fallback message
#                             return "The model was unable to provide a response due to content restrictions. Please modify your query."
#                     except Exception as e:
#                         logger.error(
#                             f"{log_prefix}Error retrieving output from LLM: {str(e)}\n"
#                             f"Response: {response_data}\nPrompt: {prompt[:100]}..."
#                         )
#                         raise
#
#                     # Track usage
#                     if "usage" in response_data:
#                         input_tokens = response_data["usage"]["prompt_tokens"]
#                         output_tokens = response_data["usage"]["completion_tokens"]
#
#                         # Update our token usage estimate for future calls
#                         if estimated_input_tokens > 0:
#                             token_ratio = input_tokens / estimated_input_tokens
#                             if token_ratio > 1.5 or token_ratio < 0.5:
#                                 logger.debug(
#                                     f"{log_prefix}Token estimation was off by factor of {token_ratio:.2f} "
#                                     f"(est: {estimated_input_tokens}, actual: {input_tokens})"
#                                 )
#
#                         cost = None
#                         model_pricing_dict = OPENAI_PRICING.get(model_id)
#                         if model_pricing_dict is not None:
#                             input_cost = (
#                                 model_pricing_dict["input"] * input_tokens * 10e-6
#                             )
#                             output_cost = (
#                                 model_pricing_dict["output"] * output_tokens * 10e-6
#                             )
#                             cost = input_cost + output_cost
#
#                         llm_usage = LLMUsage(
#                             model_id=model_id,
#                             cost=cost,
#                             input_tokens=input_tokens,
#                             output_tokens=output_tokens,
#                         )
#
#                         if response_usage is not None:
#                             response_usage.add_llm_usage(llm_usage)
#
#                     # Cache the response
#                     try:
#                         with open(cache_filepath, "w", encoding="utf-8") as f:
#                             f.write(response_msg)
#                     except Exception as e:
#                         logger.warning(f"{log_prefix}Error writing to cache file: {e}")
#
#                     return response_msg
#
#         except aiohttp.ClientError as e:
#             if retry_count < max_retries:
#                 retry_count += 1
#                 logger.warning(
#                     f"{log_prefix}Connection error: {str(e)}, retrying ({retry_count}/{max_retries})..."
#                 )
#                 await asyncio.sleep(retry_delay)
#                 retry_delay = min(retry_delay * 2, 60.0)  # Cap at 60 seconds
#             else:
#                 logger.error(
#                     f"{log_prefix}Connection failed after {max_retries} retries: {str(e)}"
#                 )
#                 raise


async def call_llm_async(
    prompt: str,
    model_id: str = OPENAI_MODEL_ID,  # This is your Azure deployment name
    max_tokens: int = 1000,
    use_cache: bool = False,
    response_usage: Optional[ResponseUsage] = None,
    current_retry: int = 0,
    max_retries: int = 5,
    retry_delay: float = 10,
    temperature: float = 0,
) -> str:
    """
    Asynchronous call to Azure OpenAI Chat Completion with caching and rate limit handling.

    Args:
        prompt: The prompt to send to the model
        model_id: The model ID (Azure deployment name)
        max_tokens: Maximum tokens to generate
        use_cache: Whether to use cached responses
        response_usage: Optional ResponseUsage object to track costs
        max_retries: Maximum number of retries for rate limit errors
        retry_delay: Initial delay before retrying (will increase exponentially)
        temperature: Temperature parameter for the model

    Returns:
        The model's response as a string
    """
    task_id = None
    # Try to extract task ID from the current context if it's in the prompt
    task_id_match = re.search(r"\[Task ([a-f0-9-]+)\]", prompt)
    if task_id_match:
        task_id = task_id_match.group(1)

    log_prefix = f"[Task {task_id}] " if task_id else ""

    # Check cache first
    cache_filepath = _generate_llm_cache_filename(prompt, model_id, max_tokens)
    if os.path.isfile(cache_filepath) and use_cache:
        try:
            with open(cache_filepath, "r", encoding="utf-8") as f:
                cached_response = f.read()
            logger.info(f"{log_prefix}Loaded async LLM response from cache.")
            return cached_response
        except Exception as e:
            logger.warning(f"{log_prefix}Error reading cache file: {e}")

    # TODO Codrin: We should use openai sdk for this.
    url = f"{AZURE_OPENAI_ENDPOINT}/openai/deployments/{model_id}/chat/completions?api-version={AZURE_API_VERSION}"

    headers = {
        "api-key": AZURE_OPENAI_API_KEY,  # Use your Azure API key here
        "Content-Type": "application/json",
    }

    payload = {
        "max_tokens": max_tokens,
        "messages": [{"role": "system", "content": prompt}],
        "temperature": temperature,
    }

    retry_count = 0

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response_obj:
                response_data = await response_obj.json()

    except Exception as e:
        if current_retry >= max_retries:
            logger.error(
                f"{log_prefix}Error calling Azure API with maximum number of retries:\n{str(e)}"
            )
            raise e

        logger.warning(
            f"{log_prefix}Error calling Azure API:\n{str(e)}\nRetrying ({retry_count}/{max_retries})..."
        )
        await asyncio.sleep(retry_delay)
        result = await call_llm_async(
            prompt=prompt,
            model_id=model_id,  # This is your Azure deployment name
            max_tokens=max_tokens,
            use_cache=use_cache,
            response_usage=response_usage,
            current_retry=current_retry + 1,
            max_retries=max_retries,
            retry_delay=retry_delay,
            temperature=temperature,
        )
        return result

    try:
        if response_data.get("error") is not None:
            error = response_data["error"]
            error_code = error.get("code", None)

            # RateLimit error
            if error_code == "429" or error_code == "rate_limit_exceeded":
                logger.warning(f"{log_prefix}Rate limit reached. Waiting... ")
                await asyncio.sleep(retry_delay)
                result = await call_llm_async(
                    prompt=prompt,
                    model_id=model_id,  # This is your Azure deployment name
                    max_tokens=max_tokens,
                    use_cache=use_cache,
                    response_usage=response_usage,
                    current_retry=current_retry + 1,
                    max_retries=max_retries,
                    retry_delay=retry_delay,
                    temperature=temperature,
                )
                return result

            if (
                error_code == "500"
                or error.get("type") == "server_error"
                or "500" in str(error)
            ):
                logger.warning(f"{log_prefix}Internal server error. Waiting... ")
                await asyncio.sleep(retry_delay)
                result = await call_llm_async(
                    prompt=prompt,
                    model_id=model_id,  # This is your Azure deployment name
                    max_tokens=max_tokens,
                    use_cache=use_cache,
                    response_usage=response_usage,
                    current_retry=current_retry + 1,
                    max_retries=max_retries,
                    retry_delay=retry_delay,
                    temperature=temperature,
                )
                return result

            if current_retry >= max_retries:
                logger.error(
                    f"{log_prefix}Error maintained after {max_retries} retries:\n{error}"
                )
                raise Exception(f"Error calling LLM API")

            logger.error(f"{log_prefix}Error calling API {error}. Retrying...")
            await asyncio.sleep(retry_delay)
            result = await call_llm_async(
                prompt=prompt,
                model_id=model_id,  # This is your Azure deployment name
                max_tokens=max_tokens,
                use_cache=use_cache,
                response_usage=response_usage,
                current_retry=current_retry + 1,
                max_retries=max_retries,
                retry_delay=retry_delay,
                temperature=temperature,
            )
            return result

        # Check for content filter
        finish_reason = response_data.get("choices", [{}])[0].get("finish_reason")
        if finish_reason == "content_filter":
            logger.warning(
                f"{log_prefix}Content filter triggered. Modifying prompt to avoid filtering."
            )

            # Add a stronger clinical context disclaimer to bypass content filter
            modified_prompt = (
                "IMPORTANT: This is a MEDICAL CONTEXT. The following content is strictly for clinical "
                "purposes and should not be filtered. Medical discussions may include sensitive topics "
                "that are necessary for proper diagnosis and treatment.\n\n" + prompt
            )

            result = await call_llm_async(
                prompt=modified_prompt,
                model_id=model_id,
                max_tokens=max_tokens,
                use_cache=use_cache,
                response_usage=response_usage,
                current_retry=max_retries,
                max_retries=max_retries,
                retry_delay=retry_delay,
                temperature=temperature,
            )
            return result

        if "content" in response_data["choices"][0].get("message", {}):
            response_msg = response_data["choices"][0]["message"]["content"]
        else:
            # Handle case where content is missing (could be due to partial content filtering)
            logger.warning(
                f"{log_prefix}Response missing content field. Response: {response_data}"
            )
            refusal = response_data["choices"][0].get("message", {}).get("refusal")
            if refusal:
                raise Exception(f"The AI refused to respond: {refusal}")

            # If we have a finish_reason but no content, create a fallback message
            raise Exception(
                "The model was unable to provide a response due to content restrictions. Please modify your query."
            )

        if "usage" in response_data:
            input_tokens = response_data["usage"]["prompt_tokens"]
            output_tokens = response_data["usage"]["completion_tokens"]

            cost = None
            model_pricing_dict = OPENAI_PRICING.get(model_id)
            if model_pricing_dict is not None:
                input_cost = model_pricing_dict["input"] * input_tokens * 10e-6
                output_cost = model_pricing_dict["output"] * output_tokens * 10e-6
                cost = input_cost + output_cost

            llm_usage = LLMUsage(
                model_id=model_id,
                cost=cost,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
            )

            if response_usage is not None:
                response_usage.add_llm_usage(llm_usage)
    except Exception as e:
        logger.error(f"Error parsing LLM output: {str(e)}")
        raise e

    try:
        with open(cache_filepath, "w", encoding="utf-8") as f:
            f.write(response_msg)
    except Exception as e:
        logger.warning(f"{log_prefix}Error writing to cache file: {e}")

    return response_msg


async def call_embedding_async(
    text: Union[str, List[str]],
    model_id: str = "text-embedding-3-small",
    use_cache: bool = True,
    response_usage: Optional[ResponseUsage] = None,
) -> Union[List[float], List[List[float]]]:
    """
    Asynchronous call to Azure OpenAI Embeddings with caching.

    Args:
        text: Text to embed (string or list of strings)
        model_id: Azure deployment name for the embedding model
        use_cache: Whether to use cached embeddings
        response_usage: Optional ResponseUsage object to track costs

    Returns:
        Embedding vector(s) as list of floats or list of lists of floats
    """
    # Handle both single string and list of strings
    is_batch = isinstance(text, list)
    texts = text if is_batch else [text]
    results = []

    for single_text in texts:
        # Check cache first if enabled
        cache_filepath = _generate_embedding_cache_filename(single_text, model_id)
        if os.path.isfile(cache_filepath) and use_cache:
            try:
                embedding = np.load(cache_filepath).tolist()
                results.append(embedding)
                continue
            except Exception as e:
                print(f"Error reading embedding cache file: {e}")

        # Build the Azure-specific endpoint URL
        url = f"{AZURE_OPENAI_ENDPOINT}/openai/deployments/{model_id}/embeddings?api-version={AZURE_API_VERSION}"

        headers = {
            "api-key": AZURE_OPENAI_API_KEY,
            "Content-Type": "application/json",
        }

        payload = {
            "input": single_text,
            "model": model_id,
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response_obj:
                response_data = await response_obj.json()

                if response_data.get("error") is not None:
                    if response_data["error"]["code"] == "429":  # RateLimit
                        print("RateLimit reached, waiting...")
                        await asyncio.sleep(30)
                        # Recursive call after waiting
                        return await call_embedding_async(
                            text=single_text,
                            model_id=model_id,
                            use_cache=use_cache,
                            response_usage=response_usage,
                        )
                    else:
                        raise Exception(
                            f"Azure OpenAI API error: {response_data['error']}"
                        )

                embedding = response_data["data"][0]["embedding"]

        # Calculate usage and cost
        if response_usage is not None:
            # Get token count from response if available, otherwise estimate
            input_tokens = response_data.get("usage", {}).get(
                "prompt_tokens", len(single_text.split()) * 1.3
            )  # Rough estimate

            cost = None
            model_pricing_dict = OPENAI_PRICING.get(model_id)
            if model_pricing_dict is not None:
                cost = (
                    model_pricing_dict.get("embedding", 0.0001) * input_tokens * 10e-6
                )

            llm_usage = LLMUsage(
                model_id=model_id,
                cost=cost,
                input_tokens=input_tokens,
                output_tokens=0,  # No output tokens for embeddings
            )

            response_usage.add_llm_usage(llm_usage)

        # Cache the embedding
        try:
            np.save(cache_filepath, np.array(embedding))
        except Exception as e:
            print(f"Error writing embedding to cache file: {e}")

        results.append(embedding)

    # Return single embedding or list of embeddings based on input type
    return results[0] if not is_batch else results


async def test_embeddings():
    """
    Test function for the embedding API.
    """
    response_usage = ResponseUsage()

    # Test with a single string
    single_text = "This is a test sentence for embedding."
    single_embedding = await call_embedding_async(
        text=single_text,
        use_cache=False,
        response_usage=response_usage,
    )
    print(f"Single embedding dimension: {len(single_embedding)}")

    # Test with a batch of strings
    batch_texts = [
        "This is the first test sentence.",
        "This is the second test sentence.",
        "This is the third test sentence.",
    ]
    batch_embeddings = await call_embedding_async(
        text=batch_texts,
        use_cache=False,
        response_usage=response_usage,
    )
    print(f"Batch embedding count: {len(batch_embeddings)}")
    print(f"First embedding dimension: {len(batch_embeddings[0])}")

    print(f"Usage: {response_usage}")


def test():
    # This is how to track costs:
    response_usage = ResponseUsage()

    result = asyncio.run(
        call_llm_async(
            prompt="What is the capital of France?",
            model_id="gpt-4o",
            max_tokens=1000,
            response_usage=response_usage,
            use_cache=False,
        )
    )
    print(result)

    print(response_usage)

    # Test embeddings
    asyncio.run(test_embeddings())


async def test_rate_limit(num_parallels: int = 2000, num_sequence: int = 30):

    response_usage = ResponseUsage()
    for i in range(num_sequence):
        print(f"Test {i+1} / {num_sequence}")
        tasks = []
        for _ in range(num_parallels):
            tasks.append(
                call_llm_async(
                    prompt=f"Write me a report on the history of the roman Empire",
                    model_id="gpt-4o-mini",
                    max_tokens=5000,
                    response_usage=response_usage,
                    use_cache=False,
                    temperature=0.5,
                )
            )
        results = await asyncio.gather(*tasks)
        # print(results)

    print(response_usage)


if __name__ == "__main__":
    asyncio.run(test_rate_limit())
