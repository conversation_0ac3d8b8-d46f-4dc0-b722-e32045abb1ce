import requests
import asyncio
import time
import logging
from typing import Tu<PERSON>, List, Dict, Optional, Any

from openai import AsyncOpenAI
from firecrawl import <PERSON><PERSON>rawlApp

from acva_ai._params import BRAVE_API, FIRECRAWL_API, OPENAI_API
from acva_ai.utils.prompts import GET_CONTENT
from acva_ai.llm.azure_call import call_llm_async
from acva_ai.utils.usage import ResponseUsage


class Search:
    """
    Class for performing web searches and content extraction for medical information.
    """

    def __init__(
        self, keyword: str = "prospect", response_usage: Optional[ResponseUsage] = None
    ):
        """
        Initialize the Search class.

        Args:
            keyword: The keyword to search for in content (default: "prospect")
            response_usage: Optional ResponseUsage object to track costs
        """
        self.app = FirecrawlApp(api_key=FIRECRAWL_API)
        self.client = AsyncOpenAI(api_key=OPENAI_API)
        self.keyword = keyword
        self.last_brave_request_time = 0
        self.response_usage = response_usage
        self.logger = logging.getLogger(__name__)

    async def get_urls(
        self, query: str, count: int = 5, language: str = "ro"
    ) -> requests.Response:
        """
        Get search results from Brave Search API.

        Args:
            query: The search query
            count: Number of results to return (default: 5)
            language: Search language code (default: "ro")

        Returns:
            Response from Brave Search API
        """
        current_time = time.time()
        time_since_last_request = current_time - self.last_brave_request_time

        # Rate limiting to avoid API throttling
        if time_since_last_request < 1.0:
            sleep_time = 1.0 - time_since_last_request
            await asyncio.sleep(sleep_time)

        brave_url = "https://api.search.brave.com/res/v1/web/search"
        header = {
            "Accept": "application/json",
            "Accept-Encoding": "gzip",
            "X-Subscription-Token": BRAVE_API,
        }
        params = {"q": query, "search_lang": language, "count": count}

        self.last_brave_request_time = time.time()

        try:
            response = await asyncio.to_thread(
                requests.get, brave_url, headers=header, params=params
            )
            return response
        except Exception as e:
            self.logger.error(f"Error fetching URLs: {str(e)}")
            raise

    async def get_content(self, url: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Scrape and extract relevant content from a URL.

        Args:
            url: The URL to scrape

        Returns:
            Tuple of (extracted_content, url) or (None, url) if rate limited or (None, None) if no relevant content
        """
        try:
            # Scrape the URL using Firecrawl
            scrape_result = await asyncio.to_thread(
                self.app.scrape_url, url, {"formats": ["markdown"]}
            )
            markdown_content = scrape_result.get("markdown", "")

            # Find the keyword in the content
            index = markdown_content.lower().find(self.keyword.lower())
            if index == -1:
                return None, None

            # Extract text from the keyword onwards
            extracted_text = markdown_content[index:]

            # Use LLM to process the extracted text
            response = await call_llm_async(
                prompt=GET_CONTENT.format(extracted_text=extracted_text),
                response_usage=self.response_usage,
            )
        except Exception as e:
            return None, url
        return response, url

    async def run_search(self, query: str, max_results: int = 5) -> Tuple[str, str]:
        """
        Run a search query and extract relevant content from the results.

        Args:
            query: The search query
            max_results: Maximum number of search results to process (default: 5)

        Returns:
            Tuple of (extracted_content, source_url) or ("", url) if rate limited or ("", "") if no content found
        """
        # Append prospect and language to query for better results
        enhanced_query = f"{query} {self.keyword} romana"
        response = await self.get_urls(enhanced_query, count=max_results)

        if response.status_code == 200:
            results = response.json().get("web", {}).get("results", [])

            if not results:
                self.logger.warning(f"No results found for query: {enhanced_query}")
                return "", ""

            # Create tasks for parallel audio_processing of URLs
            tasks = [
                asyncio.create_task(self.get_content(result["url"]))
                for result in results
            ]

            try:
                # Track rate-limited URLs
                rate_limited_url = None

                # Wait for first successful result
                done, pending = await asyncio.wait(
                    tasks, return_when=asyncio.FIRST_COMPLETED
                )

                # Check completed tasks
                for completed_task in done:
                    content, url = completed_task.result()
                    if content:
                        # Cancel remaining tasks if we found content
                        for task in pending:
                            task.cancel()
                        return content, url
                    elif url:  # This means we hit a rate limit but have a URL
                        rate_limited_url = url

                # Continue checking remaining tasks
                while pending:
                    done, pending = await asyncio.wait(
                        pending, return_when=asyncio.FIRST_COMPLETED
                    )

                    for completed_task in done:
                        content, url = completed_task.result()
                        if content:
                            # Cancel remaining tasks if we found content
                            for task in pending:
                                task.cancel()
                            return content, url
                        elif url:  # This means we hit a rate limit but have a URL
                            rate_limited_url = url

                # If we get here, no content was found but we might have a rate-limited URL
                if rate_limited_url:
                    self.logger.info(
                        f"Rate limited, returning URL only: {rate_limited_url}"
                    )
                    return "", rate_limited_url

                self.logger.info(
                    f"No relevant content found for query: {enhanced_query}"
                )
                return "", ""

            except Exception as e:
                self.logger.error(f"Error in run_search: {str(e)}")
                # Cancel any remaining tasks
                for task in tasks:
                    if not task.done():
                        task.cancel()

        else:
            self.logger.error(f"Search API returned status code {response.status_code}")

        return "", ""

    async def search_multiple_keywords(
        self, query: str, keywords: List[str]
    ) -> Dict[str, Tuple[str, str]]:
        """
        Search for multiple keywords related to the same query.

        Args:
            query: The base search query
            keywords: List of keywords to search for

        Returns:
            Dictionary mapping keywords to (content, url) tuples
        """
        results = {}

        for keyword in keywords:
            self.keyword = keyword
            content, url = await self.run_search(query)
            results[keyword] = (content, url)

        return results
