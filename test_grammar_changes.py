#!/usr/bin/env python3
"""
Test script to verify the grammar_check_by_chunks function changes work correctly.
"""

import asyncio
import uuid
from acva_ai.llm.scenarios.grammar import grammar_check_by_chunks, _grammar_check_by_chunks
from acva_ai.models.processing_status import ProcessingStatus
from acva_ai.models.visit_report import VisitReport
from acva_ai.utils.usage import ResponseUsage


async def test_internal_function():
    """Test the internal _grammar_check_by_chunks function."""
    print("Testing _grammar_check_by_chunks...")
    
    test_transcript = "This is a test transcript with some grammar errors. It has multiple sentences."
    response_usage = ResponseUsage()
    
    try:
        corrected_transcript, explanations = await _grammar_check_by_chunks(
            transcript=test_transcript,
            language="English",
            response_usage=response_usage
        )
        
        print(f"Original: {test_transcript}")
        print(f"Corrected: {corrected_transcript}")
        print(f"Explanations: {explanations}")
        print("✓ Internal function test passed")
        return True
        
    except Exception as e:
        print(f"✗ Internal function test failed: {e}")
        return False


async def test_main_function():
    """Test the main grammar_check_by_chunks function."""
    print("\nTesting grammar_check_by_chunks...")
    
    task_id = str(uuid.uuid4())
    test_transcript = "This is a test transcript with some grammar errors. It has multiple sentences."
    
    # Create required objects
    processing_status = ProcessingStatus(task_id=task_id)
    visit_report = VisitReport(task_id=task_id)
    response_usage = ResponseUsage()
    
    try:
        await grammar_check_by_chunks(
            task_id=task_id,
            transcript=test_transcript,
            processing_status=processing_status,
            visit_report=visit_report,
            language="English",
            response_usage=response_usage
        )
        
        print(f"Original: {test_transcript}")
        print(f"Corrected: {visit_report.transcript}")
        print(f"Explanations: {visit_report.grammar_explanations}")
        print("✓ Main function test passed")
        return True
        
    except Exception as e:
        print(f"✗ Main function test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("Running grammar function tests...\n")
    
    # Test internal function
    internal_success = await test_internal_function()
    
    # Test main function
    main_success = await test_main_function()
    
    print(f"\nTest Results:")
    print(f"Internal function: {'✓ PASS' if internal_success else '✗ FAIL'}")
    print(f"Main function: {'✓ PASS' if main_success else '✗ FAIL'}")
    
    if internal_success and main_success:
        print("\n🎉 All tests passed!")
    else:
        print("\n❌ Some tests failed!")


if __name__ == "__main__":
    asyncio.run(main())
